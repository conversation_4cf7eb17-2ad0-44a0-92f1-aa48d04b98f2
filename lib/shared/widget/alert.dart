import 'package:flutter/material.dart';
import 'package:pa_virtual/shared/colors.dart';

class Alert {
  static void open(
    BuildContext context, {
    String title = '',
    Widget? widgetTitle,
    String text = '',
    Widget? body,
    Widget? textWidget,
    String textButtonClose = 'Fechar',
    Color colorButtonClose = UnimedColors.redCancel,
    List<String>? lines,
    List<Widget>? actions,
    Function? callbackClose,
    bool barrierDismissible = false,
    bool hasButtonClose = true,
  }) async {
    List<Widget> texts = [];
    if (textWidget != null) {
      texts.add(textWidget);
    } else {
      texts.add(Text(
        text,
        style: const TextStyle(
            color: UnimedColors.grayDark2, fontWeight: FontWeight.bold),
      ));
    }

    List<Widget> actionsList = [];
    if (text.isNotEmpty && lines != null && lines.isNotEmpty) {
      texts.addAll(lines.map((l) => Text(
            l,
            style: TextStyle(
              color: unimedGreen.shade900,
            ),
          )));
    }

    if (actions != null && actions.isNotEmpty) {
      actionsList = actions;
    }

    bool canClickButtonClose = true;
    if (hasButtonClose) {
      actionsList.add(
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: colorButtonClose,
          ),
          child: Text(textButtonClose),
          onPressed: () {
            if (canClickButtonClose) {
              canClickButtonClose = false;
              Navigator.of(context).pop();

              if (callbackClose != null) {
                callbackClose();
              }
            }
          },
        ),
      );
    }

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierLabel: "",
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return PopScope(
          canPop: barrierDismissible,
          child: Transform(
            transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                //backgroundColor: unimedGreen.shade50,
                title: widgetTitle ??
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: unimedGreen.shade900,
                      ),
                    ),
                content: body ??
                    SingleChildScrollView(
                      child: Wrap(
                        alignment: WrapAlignment.center,
                        children: texts,
                      ),
                    ),
                actions: actionsList,
              ),
            ),
          ),
        );
      },
    );
  }

  static void openAlternativeLayout(
    BuildContext context, {
    Widget? title,
    String text = '',
    String textButtonClose = 'Fechar',
    List<String>? lines,
    Widget? action,
    Function? callbackClose,
    bool barrierDismissible = false,
  }) async {
    List<Widget> texts = [
      Text(
        text,
        textAlign: TextAlign.center,
        style: const TextStyle(color: UnimedColors.grayDark2),
      )
    ];
    List<Widget> actionsList = [];
    if (text.isNotEmpty && lines != null && lines.isNotEmpty) {
      texts.addAll(lines.map((l) => Text(
            l,
            style: TextStyle(
              color: unimedGreen.shade900,
            ),
          )));
    }

    if (action != null) {
      actionsList.add(action);
    }
    actionsList.add(const SizedBox(
      width: 8,
    ));
    actionsList.add(
      Expanded(
          child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: UnimedColors.redCancel,
        ),
        child: Text(textButtonClose),
        onPressed: () {
          Navigator.of(context).pop();

          if (callbackClose != null) {
            callbackClose();
          }
        },
      )),
    );

    List<Widget> rowActions = [];
    rowActions.add(Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(children: actionsList)));

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return PopScope(
          canPop: barrierDismissible,
          child: Transform(
            transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                //backgroundColor: unimedGreen.shade50,
                title: title,
                content: SingleChildScrollView(
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: texts,
                  ),
                ),
                actions: rowActions,
              ),
            ),
          ),
        );
      },
    );
  }
}

class UnimedAlertDialog extends StatelessWidget {
  const UnimedAlertDialog({
    super.key,
    required this.onPressed,
    required this.textWidget,
    this.title,
    this.iconData = Icons.info_outline,
    this.textButton = "Ok",
    this.colorIcon = UnimedColors.greenDark,
  });

  final IconData iconData;
  final VoidCallback onPressed;
  final Text textWidget;
  final String textButton;
  final Color colorIcon;
  final Widget? title;

  @override
  Widget build(context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: Center(
          child: Column(
        children: [
          Icon(
            iconData,
            size: 70.0,
            color: colorIcon,
          ),
          title ?? Container(),
        ],
      )),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            textWidget,
          ],
        ),
      ),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: unimedOrange,
          ),
          onPressed: onPressed,
          child: Text(textButton),
        ),
      ],
      backgroundColor: Colors.white,
    );
  }
}
